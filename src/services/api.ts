import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

// Base API configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '/api/v1';

class ApiError extends Error {
  constructor(public status: number, message: string, public data?: any) {
    super(message);
    this.name = 'ApiError';
  }
}

// Create axios instance with default configuration
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30 seconds timeout
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for adding auth tokens
apiClient.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = typeof window !== 'undefined' ? localStorage.getItem('authToken') : null;
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling errors
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error) => {
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response;
      throw new ApiError(
        status,
        data?.message || `HTTP ${status}: ${error.response.statusText}`,
        data
      );
    } else if (error.request) {
      // Request was made but no response received
      throw new ApiError(0, 'Network error: No response from server');
    } else {
      // Something else happened
      throw new ApiError(0, error.message || 'Unknown error occurred');
    }
  }
);

// Generic API request function
async function apiRequest<T>(
  endpoint: string,
  config: AxiosRequestConfig = {}
): Promise<T> {
  try {
    const response = await apiClient.request({
      url: endpoint,
      ...config,
    });

    // Return the data property from the response, or the entire response if no data property
    return response.data.data || response.data;
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError(0, 'Unexpected error occurred');
  }
}

// Convenience methods for different HTTP verbs
const api = {
  get: <T>(endpoint: string, config?: AxiosRequestConfig) =>
    apiRequest<T>(endpoint, { ...config, method: 'GET' }),

  post: <T>(endpoint: string, data?: any, config?: AxiosRequestConfig) =>
    apiRequest<T>(endpoint, { ...config, method: 'POST', data }),

  put: <T>(endpoint: string, data?: any, config?: AxiosRequestConfig) =>
    apiRequest<T>(endpoint, { ...config, method: 'PUT', data }),

  patch: <T>(endpoint: string, data?: any, config?: AxiosRequestConfig) =>
    apiRequest<T>(endpoint, { ...config, method: 'PATCH', data }),

  delete: <T>(endpoint: string, config?: AxiosRequestConfig) =>
    apiRequest<T>(endpoint, { ...config, method: 'DELETE' }),
};

export { api, apiRequest, apiClient, ApiError };
export type { ApiError as ApiErrorType };
