import { useQuery } from "@tanstack/react-query";
import axios from "axios";

// Types
export interface ExchangeRate {
  from: string;
  to: string;
  rate: number;
  lastUpdated: string;
}

export interface CurrencyConversion {
  amount: number;
  from: 'USD' | 'IDR';
  to: 'USD' | 'IDR';
  convertedAmount: number;
  rate: number;
}

// Query keys
export const currencyQueryKeys = {
  all: ['currency'] as const,
  rates: () => [...currencyQueryKeys.all, 'rates'] as const,
  conversion: (from: string, to: string, amount: number) => 
    [...currencyQueryKeys.all, 'conversion', from, to, amount] as const,
};

// Fallback exchange rates (updated periodically)
const FALLBACK_RATES = {
  USD_TO_IDR: 15000,
  IDR_TO_USD: 1 / 15000,
};

// Get exchange rates from API (with fallback)
export const useExchangeRatesQuery = () => {
  return useQuery({
    queryKey: currencyQueryKeys.rates(),
    queryFn: async (): Promise<{ USD_TO_IDR: number; IDR_TO_USD: number }> => {
      try {
        // Try to get real-time rates from a free API
        // Using exchangerate-api.com as an example (you can replace with your preferred service)
        const response = await axios.get('https://api.exchangerate-api.com/v4/latest/USD', {
          timeout: 5000, // 5 second timeout
        });
        
        const idrRate = response.data.rates.IDR;
        
        if (idrRate && typeof idrRate === 'number') {
          return {
            USD_TO_IDR: idrRate,
            IDR_TO_USD: 1 / idrRate,
          };
        }
        
        throw new Error('Invalid rate data');
      } catch (error) {
        console.warn('Failed to fetch real-time exchange rates, using fallback:', error);
        // Return fallback rates if API fails
        return FALLBACK_RATES;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 1, // Only retry once
    refetchOnWindowFocus: false,
  });
};

// Currency conversion hook
export const useCurrencyConversion = (
  amount: number,
  from: 'USD' | 'IDR',
  to: 'USD' | 'IDR'
) => {
  const { data: rates, isLoading, error } = useExchangeRatesQuery();

  return useQuery({
    queryKey: currencyQueryKeys.conversion(from, to, amount),
    queryFn: (): CurrencyConversion => {
      if (!rates) {
        throw new Error('Exchange rates not available');
      }

      if (from === to) {
        return {
          amount,
          from,
          to,
          convertedAmount: amount,
          rate: 1,
        };
      }

      let convertedAmount: number;
      let rate: number;

      if (from === 'USD' && to === 'IDR') {
        rate = rates.USD_TO_IDR;
        convertedAmount = amount * rate;
      } else if (from === 'IDR' && to === 'USD') {
        rate = rates.IDR_TO_USD;
        convertedAmount = amount * rate;
      } else {
        throw new Error(`Unsupported conversion: ${from} to ${to}`);
      }

      return {
        amount,
        from,
        to,
        convertedAmount,
        rate,
      };
    },
    enabled: !!rates && amount > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Utility function for quick conversion without React Query
export const convertCurrency = (
  amount: number,
  from: 'USD' | 'IDR',
  to: 'USD' | 'IDR',
  rates?: { USD_TO_IDR: number; IDR_TO_USD: number }
): number => {
  if (from === to) return amount;

  const exchangeRates = rates || FALLBACK_RATES;

  if (from === 'USD' && to === 'IDR') {
    return amount * exchangeRates.USD_TO_IDR;
  } else if (from === 'IDR' && to === 'USD') {
    return amount * exchangeRates.IDR_TO_USD;
  }

  return amount;
};

// Format currency with proper locale
export const formatCurrency = (amount: number, currency: 'USD' | 'IDR'): string => {
  const locale = currency === 'USD' ? 'en-US' : 'id-ID';
  
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: currency === 'IDR' ? 0 : 2,
    maximumFractionDigits: currency === 'IDR' ? 0 : 2,
  }).format(amount);
};

// Get currency symbol
export const getCurrencySymbol = (currency: 'USD' | 'IDR'): string => {
  return currency === 'USD' ? '$' : 'Rp';
};

// Currency display component data
export const getCurrencyDisplayData = (currency: 'USD' | 'IDR') => {
  return {
    code: currency,
    symbol: getCurrencySymbol(currency),
    name: currency === 'USD' ? 'US Dollar' : 'Indonesian Rupiah',
    locale: currency === 'USD' ? 'en-US' : 'id-ID',
  };
};

// Hook for currency selector options
export const useCurrencyOptions = () => {
  return [
    {
      value: 'USD',
      label: 'USD ($)',
      symbol: '$',
      name: 'US Dollar',
    },
    {
      value: 'IDR',
      label: 'IDR (Rp)',
      symbol: 'Rp',
      name: 'Indonesian Rupiah',
    },
  ];
};

// Real-time price display hook
export const useRealTimePriceDisplay = (
  baseAmount: number,
  baseCurrency: 'USD' | 'IDR',
  displayCurrency: 'USD' | 'IDR'
) => {
  const { data: rates } = useExchangeRatesQuery();
  
  const convertedAmount = convertCurrency(baseAmount, baseCurrency, displayCurrency, rates);
  
  return {
    originalAmount: baseAmount,
    originalCurrency: baseCurrency,
    displayAmount: convertedAmount,
    displayCurrency,
    formattedPrice: formatCurrency(convertedAmount, displayCurrency),
    exchangeRate: rates ? (baseCurrency === 'USD' ? rates.USD_TO_IDR : rates.IDR_TO_USD) : null,
    isRealTime: !!rates,
  };
};
