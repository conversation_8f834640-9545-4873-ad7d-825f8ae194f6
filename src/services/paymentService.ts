import { api } from './api';

export interface CreateInvoiceData {
  orderId: string;
  amount: number;
  currency: 'IDR' | 'USD';
  customerEmail: string;
  customerName: string;
  description: string;
  successRedirectUrl?: string;
  failureRedirectUrl?: string;
}

export interface CreateEWalletData {
  orderId: string;
  amount: number;
  currency: 'IDR' | 'USD';
  ewalletType: string;
  customerPhone: string;
  customerName: string;
}

export interface InvoiceResponse {
  id: string;
  externalId: string;
  status: string;
  amount: number;
  currency: string;
  invoiceUrl: string;
  expiryDate: string;
}

export interface PaymentStatus {
  id: string;
  orderId: string;
  status: string;
  amount: number;
  currency: string;
  paidAt: string | null;
  paymentMethod: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface PaymentMethods {
  invoice: boolean;
  ewallet: string[];
  virtualAccount: string[];
  retailOutlet: string[];
}

class PaymentService {
  async createInvoice(data: CreateInvoiceData): Promise<InvoiceResponse> {
    return api.post<InvoiceResponse>('/payments/invoice', data);
  }

  async createEWalletCharge(data: CreateEWalletData): Promise<any> {
    return api.post<any>('/payments/ewallet', data);
  }

  async getInvoiceStatus(invoiceId: string): Promise<any> {
    return api.get<any>(`/payments/invoice/${invoiceId}/status`);
  }

  async getPaymentStatus(orderId: string): Promise<PaymentStatus> {
    return api.get<PaymentStatus>(`/payments/order/${orderId}/status`);
  }

  async getPaymentMethods(currency: 'IDR' | 'USD'): Promise<PaymentMethods> {
    return api.get<PaymentMethods>(`/payments/methods`, {
      params: { currency }
    });
  }

  async getPaymentHistory(params?: {
    page?: number;
    limit?: number;
    status?: string;
  }): Promise<any> {
    return api.get<any>('/payments/history', {
      params
    });
  }
}

export const paymentService = new PaymentService();
