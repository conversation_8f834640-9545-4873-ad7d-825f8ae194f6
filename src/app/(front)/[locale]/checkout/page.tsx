'use client'
import React, { useState, useEffect } from 'react'
import {
    Box,
    But<PERSON>,
    Card,
    Heading,
    Text,
    VStack,
    HStack,
    Stack,
    Input,
    Textarea,

    Image,
    Badge,
    Separator,
    Grid,
    GridItem,
} from '@chakra-ui/react'
import { Alert, AlertIcon, AlertTitle, AlertDescription } from '@/components/ui/alert'
// import { Select } from '@/components/ui/select'
import { FormControl, FormLabel, FormErrorMessage } from '@/components/ui/form'
import { FaMinus, FaPlus } from 'react-icons/fa'
import { Controller, useForm } from 'react-hook-form'
import { useProductByIdQuery } from '@/services/useProductQuery'
import { useBuyNowMutation } from '@/services/useCheckoutQuery'
import { formatUSD } from '@/utils/helpers/helper'
import { toaster } from '@/components/ui/toaster'
import { useRouter, useSearchParams } from 'next/navigation'
import FormSelectField, { SelectOption } from '@/components/ui/form/FormSelectField'
import countries from '@/datas/country.json';
import { SingleValue } from 'react-select'
import { CartItem, useCartQuery } from '@/services/useCartQuery'
import { useSession } from 'next-auth/react'
import { useCreateInvoiceMutation, usePaymentMethodsQuery, openPaymentUrl, formatCurrency } from '@/services/usePaymentQuery'

interface BuyNowFormData {
    // Shipping Address
    name: string
    address: string
    city: string
    provinceRegion: string
    zipCode: string
    country: string

    paymentMethod: 'credit_card' | 'paypal' | 'bank_transfer' | 'xendit_invoice' | 'ewallet'
    ewalletType?: 'OVO' | 'DANA' | 'LINKAJA' | 'SHOPEEPAY'
    currency: 'USD' | 'IDR'

    notes?: string
}

const BuyNowCheckoutPage = () => {
    const { data: dataSession } = useSession()
    const router = useRouter()
    const searchParams = useSearchParams()
    const productId = searchParams.get('productId')
    const initialQuantity = parseInt(searchParams.get('quantity') || '1')
    const [productCart, setProductCart] = useState<CartItem[]>([])

    const [quantity, setQuantity] = useState(initialQuantity)

    const { data: cart, isLoading } = useCartQuery(productId || '', dataSession?.user.id || '');
    const { data: product, isLoading: productLoading } = useProductByIdQuery(productId || '')
    const buyNowMutation = useBuyNowMutation()
    const createInvoiceMutation = useCreateInvoiceMutation()

    // Get payment methods based on selected currency
    const [selectedCurrency, setSelectedCurrency] = useState<'USD' | 'IDR'>('USD')
    const { data: paymentMethods } = usePaymentMethodsQuery(selectedCurrency)


    const countriesOptions = countries.map(country => ({
        value: country.alpha2,
        label: `${country.emoji} ${country.name} (${country.alpha2})`,
    }))


    const {
        register,
        handleSubmit,
        formState: { errors, isSubmitting },
        watch,
        control,
        setValue
    } = useForm<BuyNowFormData>({
        defaultValues: {
            country: '',
            paymentMethod: 'xendit_invoice',
            currency: 'USD'
        }
    })

    const paymentMethod = watch('paymentMethod')
    const watchedCurrency = watch('currency')

    // Update selected currency when form currency changes
    useEffect(() => {
        if (watchedCurrency) {
            setSelectedCurrency(watchedCurrency)
        }
    }, [watchedCurrency])

    // Currency conversion function (should use real-time rates in production)
    const convertCurrency = (amount: number, fromCurrency: 'USD' | 'IDR', toCurrency: 'USD' | 'IDR') => {
        if (fromCurrency === toCurrency) return amount

        const USD_TO_IDR_RATE = 15000 // Should be fetched from real-time API

        if (fromCurrency === 'USD' && toCurrency === 'IDR') {
            return amount * USD_TO_IDR_RATE
        } else if (fromCurrency === 'IDR' && toCurrency === 'USD') {
            return amount / USD_TO_IDR_RATE
        }

        return amount
    }

    // Format price based on selected currency
    const formatPrice = (amount: number) => {
        const convertedAmount = convertCurrency(amount, 'USD', selectedCurrency)
        return formatCurrency(convertedAmount, selectedCurrency)
    }

    // useEffect(() => {
    //     if (!productId) {
    //         router.push('/')
    //     }
    // }, [productId, router])

    // const handleQuantityChange = (newQuantity: number) => {
    //     if (newQuantity >= 1) {
    //         setQuantity(newQuantity)
    //     }
    // }

    useEffect(() => {
        if (cart) {
            const existingItem = cart?.items.filter(item => item.product.sellType === 'buy-now').map(item => item.product)
            const products = existingItem.map(item => ({
                id: item.id,
                productId: item.id,
                quantity: 1,
                price: item.priceUSD,
                product: {
                    id: item.id,
                    itemName: item.itemName,
                    slug: item.slug,
                    priceUSD: item.priceUSD,
                    images: item.images,
                    sellType: item.sellType,
                    status: item.status
                },
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            }))
            setProductCart(products)
        } else if (product) {
            const products = [{
                id: product.id,
                productId: product.id,
                quantity: 1,
                price: product.priceUSD,
                product: {
                    id: product.id,
                    itemName: product.itemName,
                    slug: product.slug,
                    priceUSD: product.priceUSD,
                    images: product.images,
                    sellType: product.sellType,
                    status: product.status
                },
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            }]
            setProductCart(products)
        }
    }, [cart, product])

    const onSubmit = async (data: BuyNowFormData) => {
        if (productCart.length === 0) {
            toaster.create({
                title: "Error",
                description: "Product not found",
                type: "error",
            })
            return
        }

        if (productCart.filter(item => item.product.sellType == 'buy-now').length != productCart.length) {
            toaster.create({
                title: "Error",
                description: "This product is not available for direct purchase",
                type: "error",
            })
            return
        }

        try {
            const orderData = {
                products: productCart.map(item => item.id),
                shippingAddress: {
                    name: data.name,
                    address: data.address,
                    city: data.city,
                    provinceRegion: data.provinceRegion,
                    zipCode: data.zipCode,
                    country: data.country,
                },
                paymentMethod: {
                    type: data.paymentMethod,
                },
                notes: data.notes
            }

            const order = await buyNowMutation.mutateAsync(orderData)

            // If using Xendit payment methods, create payment invoice
            if (data.paymentMethod === 'xendit_invoice' || data.paymentMethod === 'ewallet') {
                const paymentData = {
                    orderId: order.id,
                    currency: data.currency,
                    customerEmail: dataSession?.user?.email || '',
                    customerName: `${dataSession?.user?.firstName || ''} ${dataSession?.user?.lastName || ''}`.trim(),
                    description: `Payment for Order #${order.orderNumber}`,
                    successRedirectUrl: `${window.location.origin}/account/buying/${order.id}?payment=success`,
                    failureRedirectUrl: `${window.location.origin}/account/buying/${order.id}?payment=failed`,
                }

                const invoice = await createInvoiceMutation.mutateAsync(paymentData)

                toaster.create({
                    title: "Order Created",
                    description: `Order #${order.orderNumber} created. Redirecting to payment...`,
                    type: "success",
                })

                // Open payment URL
                setTimeout(() => {
                    openPaymentUrl(invoice.invoiceUrl)
                    // Redirect to order page to track payment status
                    router.push(`/account/buying/${order.id}`)
                }, 1000)
            } else {
                // For traditional payment methods
                toaster.create({
                    title: "Purchase Successful",
                    description: `Order #${order.orderNumber} has been created successfully!`,
                    type: "success",
                })

                // Redirect to order confirmation
                router.push(`/account/buying/${order.id}`)
            }
        } catch (error) {
            console.error('Buy now failed:', error)
        }
    }

    if (productLoading || isLoading) {
        return (
            <Box maxW="6xl" mx="auto" p={6}>
                <Text>Loading product...</Text>
            </Box>
        )
    }

    if (!productCart.length) {
        return (
            <Box maxW="6xl" mx="auto" p={6} textAlign="center">
                <Alert status="error">
                    <AlertIcon status="error" />
                    <VStack align="start" flex={1}>
                        <AlertTitle>Product not found!</AlertTitle>
                        <AlertDescription>
                            The product you're trying to purchase could not be found.
                        </AlertDescription>
                    </VStack>
                </Alert>
                <Button mt={4} onClick={() => router.push('/')}>
                    Continue Shopping
                </Button>
            </Box>
        )
    }

    // Calculate totals in USD first, then convert
    const subtotalUSD = productCart.reduce((total, item) => {
        return total + Number(item.product.priceUSD ?? 0)
    }, 0)
    const taxUSD = subtotalUSD * 0.1
    const totalUSD = subtotalUSD + taxUSD

    // Convert to selected currency for display
    const subtotal = convertCurrency(subtotalUSD, 'USD', selectedCurrency)
    const tax = convertCurrency(taxUSD, 'USD', selectedCurrency)
    const total = convertCurrency(totalUSD, 'USD', selectedCurrency)

    return (
        <Box maxW="6xl" mx="auto" p={6}>
            <Heading mb={6}>Buy Now - Checkout</Heading>

            <form onSubmit={handleSubmit(onSubmit)}>
                <Grid templateColumns={{ base: "1fr", lg: "2fr 1fr" }} gap={8}>
                    {/* Left Column - Forms */}
                    <GridItem>
                        <VStack align="stretch" gap={6}>
                            {/* Shipping Information */}
                            <Card.Root>
                                <Card.Header>
                                    <Heading size="md">Shipping Information</Heading>
                                </Card.Header>
                                <Card.Body>
                                    <FormControl isInvalid={!!errors.name}>
                                        <FormLabel>Name</FormLabel>
                                        <Input
                                            {...register('name', { required: 'Name is required' })}
                                            placeholder=""
                                        />
                                        <FormErrorMessage>
                                            {errors.name?.message}
                                        </FormErrorMessage>
                                    </FormControl>

                                    <FormControl isInvalid={!!errors.address} mt={4}>
                                        <FormLabel>Address</FormLabel>
                                        <Textarea
                                            {...register('address', { required: 'Address is required' })}
                                            placeholder="JL. Example Street No. 123"
                                            rows={3}
                                        />
                                        <FormErrorMessage>
                                            {errors.address?.message}
                                        </FormErrorMessage>
                                    </FormControl>

                                    <Grid templateColumns={{ base: "1fr", md: "1fr 1fr" }} gap={4} mt={4}>
                                        <Controller
                                            name="country"
                                            control={control}
                                            render={({ field }) => (
                                                <FormSelectField
                                                    label="Country"
                                                    required
                                                    placeholder="Select Country"
                                                    options={countriesOptions}
                                                    width="100%"
                                                    value={countriesOptions.find(opt => opt.value === field.value) || undefined}
                                                    onChange={(selectedOption) => {
                                                        const option = selectedOption as SingleValue<SelectOption>;
                                                        field.onChange(option?.value || '');
                                                    }}
                                                    errorText={errors.country?.message}
                                                />
                                            )}
                                        />
                                        <FormControl isInvalid={!!errors.provinceRegion}>
                                            <FormLabel>Provice / Region</FormLabel>
                                            <Input
                                                {...register('provinceRegion', { required: 'Province / Region is required' })}
                                                placeholder="DKI Jakarta"
                                            />
                                            <FormErrorMessage>
                                                {errors.provinceRegion?.message}
                                            </FormErrorMessage>
                                        </FormControl>
                                        <FormControl isInvalid={!!errors.city}>
                                            <FormLabel>City</FormLabel>
                                            <Input
                                                {...register('city', { required: 'City is required' })}
                                                placeholder="Jakarta"
                                            />
                                            <FormErrorMessage>
                                                {errors.city?.message}
                                            </FormErrorMessage>
                                        </FormControl>

                                        <FormControl isInvalid={!!errors.zipCode}>
                                            <FormLabel>ZIP Code</FormLabel>
                                            <Input
                                                {...register('zipCode', { required: 'ZIP code is required' })}
                                                placeholder="10001"
                                            />
                                            <FormErrorMessage>
                                                {errors.zipCode?.message}
                                            </FormErrorMessage>
                                        </FormControl>
                                    </Grid>

                                </Card.Body>
                            </Card.Root>

                            {/* Currency Selection */}
                            <Card.Root>
                                <Card.Header>
                                    <Heading size="md">Currency & Payment</Heading>
                                </Card.Header>
                                <Card.Body>
                                    <Grid templateColumns="repeat(2, 1fr)" gap={4} mb={4}>
                                        <GridItem>
                                            <Controller
                                                name="currency"
                                                control={control}
                                                rules={{ required: 'Currency is required' }}
                                                render={({ field }) => (
                                                    <FormSelectField
                                                        label="Currency"
                                                        name="currency"
                                                        options={[
                                                            { value: 'USD', label: 'USD ($)' },
                                                            { value: 'IDR', label: 'IDR (Rp)' }
                                                        ]}
                                                        placeholder="Select Currency"
                                                        errorText={errors.currency?.message}
                                                        onChange={(selectedOption) => {
                                                            const option = selectedOption as SingleValue<SelectOption>;
                                                            if (option) {
                                                                field.onChange(option.value);
                                                                setSelectedCurrency(option.value as 'USD' | 'IDR');
                                                            }
                                                        }}
                                                    />
                                                )}
                                            />
                                        </GridItem>
                                        <GridItem>
                                            <Controller
                                                name="paymentMethod"
                                                control={control}
                                                rules={{ required: 'Payment method is required' }}
                                                render={({ field }) => (
                                                    <FormSelectField
                                                        label="Payment Method"
                                                        name="paymentMethod"
                                                        options={[
                                                            { value: 'xendit_invoice', label: 'Xendit Invoice (Recommended)' },
                                                            { value: 'credit_card', label: 'Credit Card' },
                                                            { value: 'bank_transfer', label: 'Bank Transfer' },
                                                            ...(selectedCurrency === 'IDR' ? [
                                                                { value: 'ewallet', label: 'E-Wallet (OVO, DANA, etc.)' }
                                                            ] : [])
                                                        ]}
                                                        placeholder="Select Payment Method"
                                                        errorText={errors.paymentMethod?.message}
                                                        onChange={(selectedOption) => {
                                                            const option = selectedOption as SingleValue<SelectOption>;
                                                            if (option) {
                                                                field.onChange(option.value);
                                                            }
                                                        }}
                                                    />
                                                )}
                                            />
                                        </GridItem>
                                    </Grid>

                                    {/* eWallet Type Selection */}
                                    {paymentMethod === 'ewallet' && selectedCurrency === 'IDR' && (
                                        <Controller
                                            name="ewalletType"
                                            control={control}
                                            rules={{ required: 'E-wallet type is required' }}
                                            render={({ field }) => (
                                                <FormSelectField
                                                    label="E-Wallet Type"
                                                    name="ewalletType"
                                                    options={[
                                                        { value: 'OVO', label: 'OVO' },
                                                        { value: 'DANA', label: 'DANA' },
                                                        { value: 'LINKAJA', label: 'LinkAja' },
                                                        { value: 'SHOPEEPAY', label: 'ShopeePay' }
                                                    ]}
                                                    placeholder="Select E-Wallet"
                                                    errorText={errors.ewalletType?.message}
                                                    onChange={(selectedOption) => {
                                                        const option = selectedOption as SingleValue<SelectOption>;
                                                        if (option) {
                                                            field.onChange(option.value);
                                                        }
                                                    }}
                                                />
                                            )}
                                        />
                                    )}

                                    {/* Payment Method Info */}
                                    {paymentMethod === 'xendit_invoice' && (
                                        <Alert status="info" mt={4}>
                                            <AlertIcon status="info" />
                                            <VStack align="start" flex={1}>
                                                <AlertDescription>
                                                    Xendit Invoice supports multiple payment methods including credit cards, bank transfers, e-wallets, and retail outlets. You'll be redirected to a secure payment page.
                                                </AlertDescription>
                                            </VStack>
                                        </Alert>
                                    )}

                                    {paymentMethod === 'ewallet' && (
                                        <Alert status="info" mt={4}>
                                            <AlertIcon status="info" />
                                            <VStack align="start" flex={1}>
                                                <AlertDescription>
                                                    You'll be redirected to your selected e-wallet app to complete the payment.
                                                </AlertDescription>
                                            </VStack>
                                        </Alert>
                                    )}

                                    {paymentMethod === 'credit_card' && (
                                        <Alert status="info" mt={4}>
                                            <AlertIcon status="info" />
                                            <VStack align="start" flex={1}>
                                                <AlertDescription>
                                                    Credit card processing will be handled securely on the next step.
                                                </AlertDescription>
                                            </VStack>
                                        </Alert>
                                    )}

                                    {/* Available Payment Methods Display */}
                                    {paymentMethods && (
                                        <Box mt={4} p={3} bg="gray.50" borderRadius="md">
                                            <Text fontSize="sm" fontWeight="medium" mb={2}>
                                                Available for {selectedCurrency}:
                                            </Text>
                                            <Text fontSize="xs" color="gray.600">
                                                {paymentMethods.invoice && "Invoice, "}
                                                {paymentMethods.creditCard && "Credit Card, "}
                                                {paymentMethods.ewallet.length > 0 && `E-Wallets (${paymentMethods.ewallet.join(', ')}), `}
                                                {paymentMethods.virtualAccount.length > 0 && `Virtual Account (${paymentMethods.virtualAccount.join(', ')}), `}
                                                {paymentMethods.retailOutlet.length > 0 && `Retail Outlets (${paymentMethods.retailOutlet.join(', ')})`}
                                            </Text>
                                        </Box>
                                    )}
                                </Card.Body>
                            </Card.Root>

                            {/* Order Notes */}
                            <Card.Root>
                                <Card.Header>
                                    <Heading size="md">Order Notes (Optional)</Heading>
                                </Card.Header>
                                <Card.Body>
                                    <FormControl>
                                        <FormLabel>Special Instructions</FormLabel>
                                        <Textarea
                                            {...register('notes')}
                                            placeholder="Any special delivery instructions or notes..."
                                            rows={3}
                                        />
                                    </FormControl>
                                </Card.Body>
                            </Card.Root>
                        </VStack>
                    </GridItem>

                    {/* Right Column - Order Summary */}
                    <GridItem>
                        <Card.Root position="sticky" top={6}>
                            <Card.Header>
                                <Heading size="md">Order Summary</Heading>
                            </Card.Header>
                            <Card.Body>
                                <VStack align="stretch" gap={4}>
                                    {/* Product */}
                                    {
                                        productCart.map(item => (
                                            <HStack key={item.id} gap={3}>
                                                <Image
                                                    src={item.product.images.find(img => img.isMain)?.imageUrl}
                                                    alt={item.product.itemName}
                                                    boxSize="80px"
                                                    objectFit="cover"
                                                    borderRadius="md"
                                                />
                                                <VStack align="start" flex={1} gap={1}>
                                                    <Text fontSize="sm" fontWeight="medium" lineClamp={2}>
                                                        {item.product.itemName}
                                                    </Text>
                                                    <Badge colorScheme="green">
                                                        Buy Now
                                                    </Badge>
                                                    <Text fontSize="sm" color="gray.800" fontWeight="bold">
                                                        {formatUSD(Number(item.product.priceUSD))}
                                                    </Text>
                                                </VStack>
                                            </HStack>
                                        ))
                                    }

                                    {/* Quantity Selector */}
                                    {/* <Box>
                                        <Text fontSize="sm" fontWeight="medium" mb={2}>Quantity</Text>
                                        <HStack>
                                            <IconButton
                                                aria-label="Decrease quantity"
                                                size="sm"
                                                onClick={() => handleQuantityChange(quantity - 1)}
                                                disabled={quantity <= 1}
                                            >
                                                <FaMinus />
                                            </IconButton>
                                            <Text minW="40px" textAlign="center" fontWeight="bold">
                                                {quantity}
                                            </Text>
                                            <IconButton
                                                aria-label="Increase quantity"
                                                size="sm"
                                                onClick={() => handleQuantityChange(quantity + 1)}
                                            >
                                                <FaPlus />
                                            </IconButton>
                                        </HStack>
                                    </Box> */}

                                    <Separator />

                                    {/* Order Totals */}
                                    <VStack align="stretch" gap={2}>
                                        <HStack justify="space-between">
                                            <Text>Subtotal:</Text>
                                            <Text>{formatUSD(subtotal)}</Text>
                                        </HStack>
                                        <HStack justify="space-between">
                                            <Text>Shipping:</Text>
                                            <Text>Free</Text>
                                        </HStack>
                                        <HStack justify="space-between">
                                            <Text>Tax:</Text>
                                            <Text>{formatUSD(tax)}</Text>
                                        </HStack>
                                        <Separator />
                                        <HStack justify="space-between">
                                            <Text fontWeight="bold" fontSize="lg">Total:</Text>
                                            <Text fontWeight="bold" fontSize="lg" color="gray.800x">
                                                {formatUSD(total)}
                                            </Text>
                                        </HStack>
                                    </VStack>

                                    <Button
                                        type="submit"
                                        colorScheme="blue"
                                        size="lg"
                                        w="full"
                                        borderRadius="full"
                                        loading={isSubmitting || buyNowMutation.isPending}
                                        disabled={isSubmitting || buyNowMutation.isPending}
                                    >
                                        Complete Purchase
                                    </Button>

                                    <Text fontSize="xs" color="gray.500" textAlign="center">
                                        By placing this order, you agree to our Terms of Service and Privacy Policy.
                                    </Text>
                                </VStack>
                            </Card.Body>
                        </Card.Root>
                    </GridItem>
                </Grid>
            </form>
        </Box>
    )
}

export default BuyNowCheckoutPage
