import { OpenAPIHono, createRoute, z } from "@hono/zod-openapi";
import { errorResponse } from "../utils/response.util";
import { formatZodError } from "../utils/format-zod-error.util";
import {
  createInvoiceSchema,
  createEWalletChargeSchema,
  webhookPayloadSchema,
  invoiceResponseSchema,
  paymentStatusResponseSchema,
  paymentMethodsResponseSchema,
  getInvoiceStatusSchema,
  getPaymentMethodsSchema,
  paymentHistoryQuerySchema,
  paymentHistoryResponseSchema,
} from "../schemas/payment.schema";
import paymentController from "../controllers/payment.controller";
import { authMiddleware } from "../middlewares/auth";

const paymentRoutes = new OpenAPIHono({
  defaultHook: (result, c) => {
    if (!result.success) {
      return c.json(
        errorResponse("Validation failed", formatZodError(result.error)),
        422
      );
    }
  },
});

// Create Invoice Route
const createInvoiceRoute = createRoute({
  method: "post",
  path: "/invoice",
  middleware: [authMiddleware] as const,
  request: {
    body: {
      content: {
        "application/json": {
          schema: createInvoiceSchema,
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: invoiceResponseSchema,
          }),
        },
      },
      description: "Invoice created successfully",
    },
    400: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Bad request",
    },
    404: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Order not found",
    },
  },
  tags: ["Payment"],
});

// Create eWallet Charge Route
const createEWalletChargeRoute = createRoute({
  method: "post",
  path: "/ewallet",
  middleware: [authMiddleware] as const,
  request: {
    body: {
      content: {
        "application/json": {
          schema: createEWalletChargeSchema,
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any(),
          }),
        },
      },
      description: "eWallet charge created successfully",
    },
    400: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Bad request",
    },
  },
  tags: ["Payment"],
});

// Get Invoice Status Route
const getInvoiceStatusRoute = createRoute({
  method: "get",
  path: "/invoice/{invoiceId}/status",
  middleware: [authMiddleware] as const,
  request: {
    params: z.object({
      invoiceId: z.string().min(1, "Invoice ID is required"),
    }),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: z.any(),
          }),
        },
      },
      description: "Invoice status retrieved successfully",
    },
    404: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Payment not found",
    },
  },
  tags: ["Payment"],
});

// Get Payment Status Route
const getPaymentStatusRoute = createRoute({
  method: "get",
  path: "/order/{orderId}/status",
  middleware: [authMiddleware] as const,
  request: {
    params: z.object({
      orderId: z.string().uuid("Invalid order ID"),
    }),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: paymentStatusResponseSchema,
          }),
        },
      },
      description: "Payment status retrieved successfully",
    },
    404: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Payment not found",
    },
  },
  tags: ["Payment"],
});

// Webhook Route (no auth required)
const webhookRoute = createRoute({
  method: "post",
  path: "/webhook",
  request: {
    body: {
      content: {
        "application/json": {
          schema: webhookPayloadSchema,
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Webhook processed successfully",
    },
  },
  tags: ["Payment"],
});

// Get Payment Methods Route
const getPaymentMethodsRoute = createRoute({
  method: "get",
  path: "/methods",
  middleware: [authMiddleware] as const,
  request: {
    query: z.object({
      currency: z.enum(["IDR", "USD"]),
    }),
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: paymentMethodsResponseSchema,
          }),
        },
      },
      description: "Payment methods retrieved successfully",
    },
    400: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
          }),
        },
      },
      description: "Invalid currency",
    },
  },
  tags: ["Payment"],
});

// Get Payment History Route
const getPaymentHistoryRoute = createRoute({
  method: "get",
  path: "/history",
  middleware: [authMiddleware] as const,
  request: {
    query: paymentHistoryQuerySchema,
  },
  responses: {
    200: {
      content: {
        "application/json": {
          schema: z.object({
            status: z.boolean(),
            message: z.string(),
            data: paymentHistoryResponseSchema,
          }),
        },
      },
      description: "Payment history retrieved successfully",
    },
  },
  tags: ["Payment"],
});

// Register routes
paymentRoutes.openapi(createInvoiceRoute, paymentController.createInvoice);
paymentRoutes.openapi(createEWalletChargeRoute, paymentController.createEWalletCharge);
paymentRoutes.openapi(getInvoiceStatusRoute, paymentController.getInvoiceStatus);
paymentRoutes.openapi(getPaymentStatusRoute, paymentController.getPaymentStatus);
paymentRoutes.openapi(webhookRoute, paymentController.handleWebhook);
paymentRoutes.openapi(getPaymentMethodsRoute, paymentController.getPaymentMethods);
paymentRoutes.openapi(getPaymentHistoryRoute, paymentController.getPaymentHistory);

export { paymentRoutes };
